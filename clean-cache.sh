#!/bin/bash

# 设置错误时退出
# This command ensures that the script will exit immediately if any command fails.
set -e

echo "🧹 Starting comprehensive cache cleanup..."

# 清除 Next.js 构建缓存
echo "📦 Cleaning Next.js build cache..."
rm -rf apps/web/.next
rm -rf apps/h5/.next

# 清除 Turbo 缓存
echo "⚡ Cleaning Turbo cache..."
rm -rf .turbo
rm -rf node_modules/.cache/turbo

# 清除 node_modules
echo "📁 Cleaning node_modules..."
rm -rf node_modules
rm -rf apps/*/node_modules
rm -rf packages/*/node_modules

# 清除 PM2 缓存和日志
echo "🔄 Cleaning PM2 cache and logs..."
rm -rf apps/web/deploy/.pm2
rm -rf apps/h5/deploy/.pm2
rm -rf apps/web/deploy/logs/*
rm -rf apps/h5/deploy/logs/*

# 清除 TypeScript 缓存 (使用 find 命令更可靠)
echo "📝 Cleaning TypeScript cache..."
find apps packages -name "*.tsbuildinfo" -delete 2>/dev/null || true

# 清除 ESLint 缓存
echo "🔍 Cleaning ESLint cache..."
rm -f .eslintcache
find . -name ".eslintcache" -delete 2>/dev/null || true

# 清除 Prettier 缓存
echo "💅 Cleaning Prettier cache..."
rm -f .prettiercache

# 清除临时文件和目录
echo "🗑️  Cleaning temporary files..."
rm -rf .tmp .temp
rm -rf apps/*/.tmp apps/*/.temp
rm -rf packages/*/.tmp packages/*/.temp

# 清除可能的构建产物
echo "🏗️  Cleaning build artifacts..."
rm -rf dist build
rm -rf apps/*/dist apps/*/build
rm -rf packages/*/dist packages/*/build

# 清除 GraphQL 生成文件 (常见位置)
echo "🔗 Cleaning GraphQL generated files..."
find apps packages -name "generated" -type d -exec rm -rf {} + 2>/dev/null || true
find apps packages -name "*.generated.*" -delete 2>/dev/null || true

# 清除 pnpm 缓存 (可选)
if command -v pnpm &> /dev/null; then
    echo "📦 Found pnpm, cleaning pnpm store..."
    pnpm store prune || true
fi

# 清除 Redis 缓存(如果 Redis 在运行)
# WARNING: This will flush ALL data from the Redis instance. Use with caution.
if command -v redis-cli &> /dev/null; then
    echo "🔴 Cleaning Redis cache..."
    redis-cli FLUSHALL 2>/dev/null || echo "⚠️  Redis not running or not accessible"
fi

echo "✅ Cache cleanup completed successfully!"
echo "💡 You may want to run 'pnpm install' to reinstall dependencies."
