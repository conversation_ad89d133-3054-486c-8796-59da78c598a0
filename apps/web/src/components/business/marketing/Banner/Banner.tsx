'use client'

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import Image from 'next/image'
import { Splide, SplideSlide } from '@splidejs/react-splide'
import { cn, RelationItem, TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'
import { But<PERSON> } from 'antd'
import clsx from 'clsx'

import { IconArrow } from '@/components'
import { Arrow } from '@/components'
import { Link } from '@/i18n/navigation'

import LazyVideoPlayer from './components/LazyVideoPlayer'
import { useBannerAnimation } from './hooks/useBannerAnimation'
import { useBannerVideoManager } from './hooks/useBannerVideoManager'

import styles from './index.module.css'

import '@splidejs/react-splide/css'

type BannerProps = {
  bannerData: RelationItem[] | null
}

interface CarouselProps {
  go: (index: number | string) => void
  splide?: {
    options: Record<string, unknown>
    refresh: () => void
  }
}

const Banner = ({ bannerData }: BannerProps) => {
  const [activeIndex, setActiveIndex] = useState(0)
  const carouselRef = useRef<CarouselProps>(null)
  const [bannerIndex, setBannerIndex] = useState(0)
  const [firstVideoLoad, setFirstVideoLoad] = useState(false)
  const bannerTimer = useRef<NodeJS.Timeout>()
  const isSwitchingRef = useRef(false) // 添加切换锁
  const { reportEvent } = useVolcAnalytics()

  // 使用性能优化的自定义hooks
  const { startAnimation, stopAnimation } = useBannerAnimation()

  const bannerDataFiltered = useMemo(() => {
    return bannerData
      ?.filter((item) => item?.button_url?.type !== 'seckill')
      .filter((item) => !!item?.image_url_desktop)
  }, [bannerData])

  const bannerTotal = useMemo(() => {
    return bannerDataFiltered?.length || 0
  }, [bannerDataFiltered])

  // 视频管理hook
  const {
    videoListRef,
    shouldLoadVideo,
    setVideoRef,
    playVideo,
    pauseVideo,
    resetVideo,
    cleanupVideos,
  } = useBannerVideoManager(bannerDataFiltered || null, activeIndex)

  // 优化后的进度条动画控制
  const playDotsAnimation = useCallback(
    (index: number) => {
      const elems = document.querySelectorAll('.progressLine')
      const len = elems.length
      const progressBar = document.querySelectorAll('.progressLine')[index] as HTMLElement
      let duration = 5000 // 默认图片停留时间

      if (!progressBar) return

      stopAnimation()

      // 处理视频的播放
      const video = videoListRef.current[index]
      if (video && bannerDataFiltered?.[index]?.pc_video_url) {
        if (video.readyState >= 2) {
          duration = video.duration * 1000
          // 延迟播放，确保元素已渲染
          setTimeout(() => playVideo(index), 50)
        } else {
          // 等待视频加载完成
          const handleLoadedData = () => {
            if (video.duration) {
              duration = video.duration * 1000
            }
            // 延迟播放，确保元素已渲染
            setTimeout(() => playVideo(index), 50)
            video.removeEventListener('loadeddata', handleLoadedData)
          }
          video.addEventListener('loadeddata', handleLoadedData)
        }

        // 对于有视频的项，不使用动画回调来切换，而是依赖视频的onEnded事件
        startAnimation(progressBar, elems, index, len, duration, video, undefined)
      } else {
        // 对于图片，使用动画完成回调来切换
        startAnimation(progressBar, elems, index, len, duration, video, () =>
          carouselRef.current?.go('>'),
        )
      }
    },
    [bannerDataFiltered, startAnimation, stopAnimation, playVideo, videoListRef],
  )

  const handleNext = () => {
    stopDotsAnimation()
    let index = bannerIndex + 1
    if (index > bannerTotal - 1) {
      index = 0
    }
    handleClickDot(index)
  }

  const handlePrev = () => {
    let index = bannerIndex - 1
    if (index < 0) {
      index = bannerTotal - 1
    }
    handleClickDot(index)
  }

  const stopDotsAnimation = useCallback(() => {
    stopAnimation()
    if (videoListRef.current[activeIndex]) {
      pauseVideo(activeIndex)
    }
  }, [activeIndex, stopAnimation, pauseVideo, videoListRef])

  const handleClickDot = useCallback(
    (index: number) => {
      if (index === bannerIndex) return

      // 重置切换锁
      isSwitchingRef.current = false
      stopDotsAnimation()

      const elems = document.querySelectorAll('.progressLine')
      elems.forEach((item, j) => {
        const element = item as HTMLElement
        if (index > j) {
          element.style.transform = 'scaleX(1)'
        } else {
          element.style.transform = 'scaleX(0)'
        }
      })

      // 如果切换到第一项且当前在最后一项，重置所有进度条
      if (index === 0 && bannerIndex === bannerTotal - 1) {
        elems.forEach((item) => {
          ;(item as HTMLElement).style.transform = 'scaleX(0)'
        })
      }

      carouselRef.current?.go(index)
    },
    [bannerIndex, bannerTotal, stopDotsAnimation],
  )

  const handleSideChange = useCallback(
    (index: number) => {
      setActiveIndex(index)

      // 暂停所有其他视频，播放当前视频
      videoListRef.current.forEach((_, i) => {
        if (i === index) {
          resetVideo(i)
          if (bannerDataFiltered?.[i]?.pc_video_url) {
            playVideo(i)
          }
        } else {
          pauseVideo(i)
          resetVideo(i)
        }
      })

      playDotsAnimation(index)
    },
    [bannerDataFiltered, resetVideo, playVideo, pauseVideo, playDotsAnimation, videoListRef],
  )

  // Banner曝光埋点
  useEffect(() => {
    if (bannerDataFiltered && bannerDataFiltered.length > 0) {
      bannerDataFiltered.forEach((item) => {
        // 上报banner图片曝光事件
        reportEvent(TRACK_EVENT.shop_homepage_banner_picture_exposure, {
          banner_id: item?.id || '',
          banner_name: item?.title || '',
        })

        // 上报top banner图片曝光事件
        reportEvent(TRACK_EVENT.shop_homepage_top_banner_picture_exposure, {
          banner_id: item?.id || '',
          banner_name: item?.title || '',
        })
      })
    }
  }, [bannerDataFiltered, reportEvent])

  // 初始化第一个banner的播放
  useEffect(() => {
    if (!bannerDataFiltered?.[0]?.pc_video_url) {
      setFirstVideoLoad(true)
      // 延迟启动动画，确保组件完全渲染
      const timer = setTimeout(() => {
        playDotsAnimation(0)
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [bannerDataFiltered, playDotsAnimation])

  // 清理时候的内存泄露防护
  useEffect(() => {
    const currentBannerTimer = bannerTimer.current
    return () => {
      stopAnimation()
      cleanupVideos()
      if (currentBannerTimer) {
        clearInterval(currentBannerTimer)
      }
    }
  }, [stopAnimation, cleanupVideos])

  const handleBeforeChange = useCallback(
    (_from: number, to: number) => {
      // 确保索引在有效范围内
      if (to >= 0 && (!bannerDataFiltered || to < bannerDataFiltered?.length)) {
        handleClickDot(to)
      }
    },
    [bannerDataFiltered, handleClickDot],
  )
  // 优化后的视频组件渲染
  const renderVideo = useCallback(
    (item: RelationItem, index: number) => {
      const isActive = index === activeIndex

      const handleVideoLoadedData = () => {
        // 视频准备好后，如果是第一个视频且需要加载
        if (index === 0 && !firstVideoLoad) {
          setFirstVideoLoad(true)
          setTimeout(() => playDotsAnimation(0), 100)
        }
      }

      const handleVideoEnded = () => {
        // 视频播放完成，切换到下一个
        if (index === activeIndex && !isSwitchingRef.current) {
          isSwitchingRef.current = true

          const progressLine = document.querySelectorAll('.progressLine')[index] as HTMLElement
          if (progressLine) {
            progressLine.style.transform = 'scaleX(1)'
          }

          setTimeout(() => {
            if (index === activeIndex) {
              const nextIndex = (index + 1) % (bannerDataFiltered?.length || 1)
              handleBeforeChange(index, nextIndex)
            }
            // 延迟重置锁，避免快速触发
            setTimeout(() => {
              isSwitchingRef.current = false
            }, 300)
          }, 100)
        }
      }

      return (
        <div className="absolute inset-0 h-full w-full overflow-hidden">
          <LazyVideoPlayer
            ref={setVideoRef(index)}
            src={item?.pc_video_url || ''}
            isActive={isActive}
            shouldLoad={shouldLoadVideo(index)}
            className="absolute inset-0 h-full w-full"
            style={{
              objectFit: 'cover',
              width: '100%',
              height: '100%',
            }}
            onLoadedData={handleVideoLoadedData}
            onEnded={handleVideoEnded}
          />
        </div>
      )
    },
    [
      activeIndex,
      shouldLoadVideo,
      setVideoRef,
      firstVideoLoad,
      playDotsAnimation,
      bannerDataFiltered,
      handleBeforeChange,
    ],
  )

  const getBannerHeight = () => {
    // 根据屏幕宽度计算Banner高度
    const width = window.innerWidth
    if (width >= 1920) {
      return 800
    } else if (width >= 1440) {
      const height = 597 + ((width - 1440) / 480) * 203
      return Math.round(height)
    } else if (width >= 1024) {
      // 响应式计算高度 - 与CSS中的clamp函数保持一致
      const height = 424 + ((width - 1024) / 416) * 173
      return Math.round(height)
    } else {
      return 424
    }
  }

  const [bannerWidth, setBannerWidth] = useState(1024)

  // 添加窗口大小变化监听
  useEffect(() => {
    const handleResize = () => {
      const realWidth = Math.max(document.documentElement.clientWidth, 1024)
      setBannerWidth(realWidth)
      if (carouselRef.current?.splide) {
        carouselRef.current.splide.options = {
          ...carouselRef.current.splide.options,
          height: getBannerHeight(),
          width: realWidth,
        }
        carouselRef.current.splide.refresh()
      }
    }

    window.addEventListener('resize', handleResize)
    // 初始化时也执行一次
    handleResize()

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return (
    <div
      className="group relative mx-auto w-full max-w-[2560px] overflow-hidden"
      style={{
        width: bannerWidth,
        height: getBannerHeight(),
      }}>
      <Splide
        ref={carouselRef}
        options={{
          type: 'loop',
          pagination: !1,
          drag: !1,
          arrows: !1,
          rewind: !0,
          perMove: 1,
          preloadPages: 2,
          interval: 5000,
          height: getBannerHeight(), // 使用动态计算的高度
          width: '100%',
        }}
        onMove={(_e: object, a: number) => {
          stopDotsAnimation()
          setBannerIndex(a)
          handleSideChange(a)
        }}>
        {bannerDataFiltered?.map((item, index) => (
          <SplideSlide
            key={index}
            onMouseEnter={() => {
              stopDotsAnimation()
            }}
            onMouseLeave={() => {
              playDotsAnimation(index)
            }}>
            <Link
              className="relative block h-full w-full"
              href={item?.button_url?.url || '#'}
              onClick={(e) => {
                if (!item?.button_url?.url) {
                  e.preventDefault()
                  return
                }

                // 上报banner图片点击事件
                reportEvent(TRACK_EVENT.shop_homepage_banner_picture_click, {
                  banner_id: item?.id || '',
                  banner_name: item?.title || '',
                })

                // 上报top banner图片点击事件
                reportEvent(TRACK_EVENT.shop_homepage_top_banner_picture_click, {
                  banner_id: item?.id || '',
                  banner_name: item?.title || '',
                })
              }}>
              <div className="absolute inset-0 flex h-full w-full flex-col items-center overflow-hidden">
                {item?.pc_video_url ? (
                  renderVideo(item, index)
                ) : (
                  <div className="absolute inset-0 h-full w-full overflow-hidden">
                    <Image
                      src={item?.image_url_desktop || ''}
                      alt={item?.title || ''}
                      fill
                      className="object-cover"
                      priority={index === 0} // 第一张图片最高优先级
                      sizes="100vw"
                      quality={index === 0 ? 95 : 85} // 第一张图片高质量
                      style={{ objectPosition: 'center center' }}
                    />
                  </div>
                )}

                {/* 内容区域 */}
                <div className="relative mx-auto">
                  <div className="flex flex-col items-center pt-[66.5px] text-white">
                    {item?.subtitle && (
                      <div className="mb-[8px] truncate text-center font-miSansMedium380 text-[18px] leading-[120%] 2xl:text-[20px]">
                        {item?.subtitle}
                      </div>
                    )}
                    {item?.title && (
                      <div className="mb-[8px] truncate text-center font-miSansSemibold520 text-[40px] leading-[120%] 2xl:text-[64px]">
                        {item?.title}
                      </div>
                    )}
                    {item?.description && (
                      <div className="mb-[24px] truncate text-center font-miSansMedium380 text-[16px] leading-[140%] 2xl:text-[24px]">
                        {item?.description}
                      </div>
                    )}
                    {item?.button_text && (
                      <Button
                        className={clsx(
                          'min-w-[146px] font-miSansDemiBold450 text-[16px] leading-[20px]',
                          item?.button_arrow ? 'justify-between' : 'justify-center',
                        )}
                        type="primary"
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()

                          // 上报查看详情按钮点击事件
                          reportEvent(TRACK_EVENT.shop_homepage_top_banner_detail_button_click, {
                            banner_id: item?.id || '',
                            banner_name: item?.title || '',
                          })
                        }}>
                        {item?.button_text}
                        {item.button_arrow && <IconArrow color="#fff" size={20} rotate={-90} />}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          </SplideSlide>
        ))}
      </Splide>

      {/* 选项卡 */}
      {bannerTotal > 1 && (
        <>
          <div className="absolute bottom-0 left-0 h-[80px] w-full bg-[linear-gradient(360deg,rgba(0,0,0,0.5)_-71.82%,rgba(0,0,0,0)_88.64%)] xll:h-[106px]">
            <div className="max-container-no-mb">
              <div className="mx-auto flex w-fit justify-center">
                <div className="flex h-fit items-center justify-center text-[16px] text-white">
                  {bannerDataFiltered?.map((item, index) => (
                    <div
                      key={index}
                      className={cn(
                        'relative flex cursor-pointer items-center justify-center border-t border-gray-3 px-[20px] pt-[11px]',
                      )}
                      onClick={() => {
                        handleClickDot(index)
                      }}>
                      <div className={styles.progressContainer}>
                        <div className={`progressLine ${styles.progressLine}`} />
                      </div>

                      <span
                        className={cn(
                          'relative flex h-[54px] items-center whitespace-nowrap px-[5px] text-[16px] leading-[22px] text-white',
                          activeIndex === index
                            ? '!text-white before:font-miSansSemibold520 before:!text-white before:!text-opacity-100'
                            : '!text-opacity-60',
                          {
                            'invisible before:visible before:absolute before:inset-0 before:flex before:items-center before:justify-center before:content-[attr(data-value)]':
                              activeIndex === index,
                          },
                        )}
                        data-value={item?.bottom_title?.trim() || ''}>
                        {item?.bottom_title?.trim() || ''}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 top-0 ml-[48px] hidden text-white group-hover:block">
            <div className="flex h-full items-center justify-center">
              <button
                className="flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full bg-black/40 transition-colors duration-200 hover:bg-black/30 focus:bg-black/65 focus:outline-none active:bg-black/65"
                onClick={handlePrev}
                onMouseLeave={(e) => e.currentTarget.blur()}>
                <Arrow rotate={180} color="currentColor" />
              </button>
            </div>
          </div>
          <div className="absolute bottom-0 right-0 top-0 mr-[48px] hidden text-white group-hover:block">
            <div className="flex h-full items-center justify-center">
              <button
                className="flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full bg-black/40 transition-colors duration-200 hover:bg-black/30 focus:bg-black/65 focus:outline-none active:bg-black/65"
                onClick={handleNext}
                onMouseLeave={(e) => e.currentTarget.blur()}>
                <Arrow color="currentColor" />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default Banner
