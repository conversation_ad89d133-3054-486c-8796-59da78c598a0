'use client'

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import Image from 'next/image'
import { Splide, SplideSlide } from '@splidejs/react-splide'
import { cn, RelationItem, TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'
import { But<PERSON> } from 'antd'
import clsx from 'clsx'

import { IconArrow } from '@/components'
import { Arrow } from '@/components'
import { Link } from '@/i18n/navigation'

import LazyVideoPlayer from './components/LazyVideoPlayer'
import { useBannerAnimation } from './hooks/useBannerAnimation'
import { useBannerVideoManager } from './hooks/useBannerVideoManager'

import styles from './index.module.css'

import '@splidejs/react-splide/css'

type BannerProps = {
  bannerData: RelationItem[] | null
}

interface CarouselProps {
  go: (index: number | string) => void
  splide?: {
    options: Record<string, unknown>
    refresh: () => void
  }
}

const Banner = ({ bannerData }: BannerProps) => {
  const [activeIndex, setActiveIndex] = useState(0)
  const carouselRef = useRef<CarouselProps>(null)
  const [bannerIndex, setBannerIndex] = useState(0)
  const [firstVideoLoad, setFirstVideoLoad] = useState(false)
  const bannerTimer = useRef<NodeJS.Timeout>()
  const isSwitchingRef = useRef(false) // 添加切换锁
  const videoLoadedRef = useRef<Set<number>>(new Set()) // 跟踪已加载的视频
  const animationStartedRef = useRef<Set<number>>(new Set()) // 跟踪已开始动画的视频
  const lastSwitchTimeRef = useRef<number>(0) // 记录最后一次切换时间，用于防抖
  const videoDurationsRef = useRef<Map<number, number>>(new Map()) // 存储每个视频的时长
  const { reportEvent } = useVolcAnalytics()

  // 使用性能优化的自定义hooks
  const { startAnimation, stopAnimation } = useBannerAnimation()

  const bannerDataFiltered = useMemo(() => {
    return bannerData
      ?.filter((item) => item?.button_url?.type !== 'seckill')
      .filter((item) => !!item?.image_url_desktop)
  }, [bannerData])

  const bannerTotal = useMemo(() => {
    return bannerDataFiltered?.length || 0
  }, [bannerDataFiltered])

  // 视频管理hook
  const {
    videoListRef,
    shouldLoadVideo,
    setVideoRef,
    playVideo,
    pauseVideo,
    resetVideo,
    cleanupVideos,
  } = useBannerVideoManager(bannerDataFiltered || null, activeIndex)

  // 处理视频时长回调
  const handleVideoDuration = useCallback((index: number, duration: number) => {
    videoDurationsRef.current.set(index, duration * 1000) // 转换为毫秒
    console.log(`视频 ${index} 时长已设置: ${duration}秒`)
  }, [])

  // 优化后的进度条动画控制
  const playDotsAnimation = useCallback(
    (index: number) => {
      const elems = document.querySelectorAll('.progressLine')
      const len = elems.length
      const progressBar = document.querySelectorAll('.progressLine')[index] as HTMLElement
      let duration = 5000 // 默认图片停留时间

      if (!progressBar) return

      // 防止重复启动动画
      if (animationStartedRef.current.has(index)) {
        return
      }

      stopAnimation()
      animationStartedRef.current.add(index)

      // 处理视频的播放
      const video = videoListRef.current[index]
      if (video && bannerDataFiltered?.[index]?.pc_video_url) {
        // 使用存储的视频时长，如果没有则使用默认值
        const storedDuration = videoDurationsRef.current.get(index)
        if (storedDuration && storedDuration > 0) {
          duration = storedDuration
          console.log(`使用存储的视频时长: ${duration}ms`)
        } else {
          console.warn(`视频 ${index} 时长未获取到，使用默认时长: ${duration}ms`)
        }

        videoLoadedRef.current.add(index)
        // 延迟播放，确保元素已渲染
        setTimeout(() => playVideo(index), 50)

        // 对于有视频的项，不使用动画回调来切换，而是依赖视频的onEnded事件
        startAnimation(progressBar, elems, index, len, duration, video, undefined)
      } else {
        // 对于图片，使用动画完成回调来切换
        startAnimation(progressBar, elems, index, len, duration, video, () =>
          carouselRef.current?.go('>'),
        )
      }
    },
    [bannerDataFiltered, startAnimation, stopAnimation, playVideo, videoListRef],
  )

  const handleNext = () => {
    stopDotsAnimation()
    let index = bannerIndex + 1
    if (index > bannerTotal - 1) {
      index = 0
    }
    handleClickDot(index)
  }

  const handlePrev = () => {
    let index = bannerIndex - 1
    if (index < 0) {
      index = bannerTotal - 1
    }
    handleClickDot(index)
  }

  const stopDotsAnimation = useCallback(() => {
    stopAnimation()
    // 清除动画状态
    animationStartedRef.current.clear()
    if (videoListRef.current[activeIndex]) {
      pauseVideo(activeIndex)
    }
  }, [activeIndex, stopAnimation, pauseVideo, videoListRef])

  const handleClickDot = useCallback(
    (index: number) => {
      if (index === bannerIndex) return

      // 重置切换锁和动画状态
      isSwitchingRef.current = false
      animationStartedRef.current.clear()
      stopDotsAnimation()

      const elems = document.querySelectorAll('.progressLine')
      elems.forEach((item, j) => {
        const element = item as HTMLElement
        if (index > j) {
          element.style.transform = 'scaleX(1)'
        } else {
          element.style.transform = 'scaleX(0)'
        }
      })

      // 如果切换到第一项且当前在最后一项，重置所有进度条
      if (index === 0 && bannerIndex === bannerTotal - 1) {
        elems.forEach((item) => {
          ;(item as HTMLElement).style.transform = 'scaleX(0)'
        })
      }

      carouselRef.current?.go(index)
    },
    [bannerIndex, bannerTotal, stopDotsAnimation],
  )

  const handleSideChange = useCallback(
    (index: number) => {
      setActiveIndex(index)

      // 暂停所有其他视频，播放当前视频
      videoListRef.current.forEach((_, i) => {
        if (i === index) {
          resetVideo(i)
          if (bannerDataFiltered?.[i]?.pc_video_url) {
            playVideo(i)
          }
        } else {
          pauseVideo(i)
          resetVideo(i)
        }
      })

      // 只有在动画还没开始时才启动动画，避免重复调用
      if (!animationStartedRef.current.has(index)) {
        playDotsAnimation(index)
      }
    },
    [bannerDataFiltered, resetVideo, playVideo, pauseVideo, playDotsAnimation, videoListRef],
  )

  // Banner曝光埋点
  useEffect(() => {
    if (bannerDataFiltered && bannerDataFiltered.length > 0) {
      bannerDataFiltered.forEach((item) => {
        // 上报banner图片曝光事件
        reportEvent(TRACK_EVENT.shop_homepage_banner_picture_exposure, {
          banner_id: item?.id || '',
          banner_name: item?.title || '',
        })

        // 上报top banner图片曝光事件
        reportEvent(TRACK_EVENT.shop_homepage_top_banner_picture_exposure, {
          banner_id: item?.id || '',
          banner_name: item?.title || '',
        })
      })
    }
  }, [bannerDataFiltered, reportEvent])

  // 初始化第一个banner的播放
  useEffect(() => {
    if (!bannerDataFiltered?.[0]?.pc_video_url) {
      setFirstVideoLoad(true)
      // 延迟启动动画，确保组件完全渲染
      const timer = setTimeout(() => {
        playDotsAnimation(0)
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [bannerDataFiltered, playDotsAnimation])

  // 清理时候的内存泄露防护
  useEffect(() => {
    const currentBannerTimer = bannerTimer.current
    const animationStarted = animationStartedRef.current
    const videoLoaded = videoLoadedRef.current
    const videoDurations = videoDurationsRef.current
    return () => {
      stopAnimation()
      cleanupVideos()
      // 清理状态
      animationStarted.clear()
      videoLoaded.clear()
      videoDurations.clear()
      if (currentBannerTimer) {
        clearInterval(currentBannerTimer)
      }
    }
  }, [stopAnimation, cleanupVideos])

  // 当 banner 数据改变时，清理相关状态
  useEffect(() => {
    animationStartedRef.current.clear()
    videoLoadedRef.current.clear()
    videoDurationsRef.current.clear()
    lastSwitchTimeRef.current = 0
    console.log('Banner 数据改变，状态已清理')
  }, [bannerDataFiltered])

  const handleBeforeChange = useCallback(
    (_from: number, to: number) => {
      // 确保索引在有效范围内
      if (to >= 0 && (!bannerDataFiltered || to < bannerDataFiltered?.length)) {
        handleClickDot(to)
      }
    },
    [bannerDataFiltered, handleClickDot],
  )
  // 优化后的视频组件渲染
  const renderVideo = useCallback(
    (item: RelationItem, index: number) => {
      const isActive = index === activeIndex

      const handleVideoLoadedData = () => {
        // 视频准备好后，如果是第一个视频且需要加载
        if (index === 0 && !firstVideoLoad && !videoLoadedRef.current.has(0)) {
          setFirstVideoLoad(true)
          setTimeout(() => playDotsAnimation(0), 100)
        }
      }

      const handleVideoEnded = () => {
        console.log(`视频 ${index} 播放结束，当前活跃索引: ${activeIndex}`)

        // 视频播放完成，切换到下一个
        const now = Date.now()
        const timeSinceLastSwitch =
          lastSwitchTimeRef.current === 0 ? 1000 : now - lastSwitchTimeRef.current

        console.log(
          `视频结束切换检查: 当前时间=${now}, 上次切换=${lastSwitchTimeRef.current}, 间隔=${timeSinceLastSwitch}ms`,
        )

        // 防抖：如果距离上次切换时间太短，忽略这次切换
        if (timeSinceLastSwitch < 1000) {
          // 增加防抖时间到1秒
          console.log(`视频 ${index} 切换被防抖阻止，距离上次切换仅 ${timeSinceLastSwitch}ms`)
          return
        }

        if (index === activeIndex && !isSwitchingRef.current) {
          console.log(`开始切换视频 ${index} 到下一个`)
          isSwitchingRef.current = true
          lastSwitchTimeRef.current = now

          const progressLine = document.querySelectorAll('.progressLine')[index] as HTMLElement
          if (progressLine) {
            progressLine.style.transform = 'scaleX(1)'
          }

          // 清除当前动画状态，允许下一个banner开始新的动画
          animationStartedRef.current.delete(index)

          setTimeout(() => {
            // 双重检查：确保当前仍然是活跃的视频且组件状态没有改变
            if (index === activeIndex && isSwitchingRef.current) {
              const nextIndex = (index + 1) % (bannerDataFiltered?.length || 1)
              console.log(`执行切换：${index} -> ${nextIndex}`)
              handleBeforeChange(index, nextIndex)
            } else {
              console.log(
                `切换被取消：index=${index}, activeIndex=${activeIndex}, isSwitching=${isSwitchingRef.current}`,
              )
            }

            // 延迟重置锁，避免快速触发
            setTimeout(() => {
              isSwitchingRef.current = false
              console.log(`切换锁已重置`)
            }, 500) // 增加重置延迟
          }, 200) // 增加切换延迟
        } else {
          console.log(
            `视频 ${index} 切换被阻止：activeIndex=${activeIndex}, isSwitching=${isSwitchingRef.current}`,
          )
        }
      }

      return (
        <div className="absolute inset-0 h-full w-full overflow-hidden">
          <LazyVideoPlayer
            ref={setVideoRef(index)}
            src={item?.pc_video_url || ''}
            isActive={isActive}
            shouldLoad={shouldLoadVideo(index)}
            className="absolute inset-0 h-full w-full"
            style={{
              objectFit: 'cover',
              width: '100%',
              height: '100%',
            }}
            onLoadedData={handleVideoLoadedData}
            onEnded={handleVideoEnded}
            onDuration={(duration) => handleVideoDuration(index, duration)}
          />
        </div>
      )
    },
    [
      activeIndex,
      shouldLoadVideo,
      setVideoRef,
      firstVideoLoad,
      playDotsAnimation,
      bannerDataFiltered,
      handleBeforeChange,
      handleVideoDuration,
    ],
  )

  const getBannerHeight = () => {
    // 根据屏幕宽度计算Banner高度
    const width = window.innerWidth
    if (width >= 1920) {
      return 800
    } else if (width >= 1440) {
      const height = 597 + ((width - 1440) / 480) * 203
      return Math.round(height)
    } else if (width >= 1024) {
      // 响应式计算高度 - 与CSS中的clamp函数保持一致
      const height = 424 + ((width - 1024) / 416) * 173
      return Math.round(height)
    } else {
      return 424
    }
  }

  const [bannerWidth, setBannerWidth] = useState(1024)

  // 添加窗口大小变化监听
  useEffect(() => {
    const handleResize = () => {
      const realWidth = Math.max(document.documentElement.clientWidth, 1024)
      setBannerWidth(realWidth)
      if (carouselRef.current?.splide) {
        carouselRef.current.splide.options = {
          ...carouselRef.current.splide.options,
          height: getBannerHeight(),
          width: realWidth,
        }
        carouselRef.current.splide.refresh()
      }
    }

    window.addEventListener('resize', handleResize)
    // 初始化时也执行一次
    handleResize()

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return (
    <div
      className="group relative mx-auto w-full max-w-[2560px] overflow-hidden"
      style={{
        width: bannerWidth,
        height: getBannerHeight(),
      }}>
      <Splide
        ref={carouselRef}
        options={{
          type: 'loop',
          pagination: !1,
          drag: !1,
          arrows: !1,
          rewind: !0,
          perMove: 1,
          preloadPages: 2,
          autoplay: false, // 禁用自动播放，完全由我们控制
          interval: 5000,
          height: getBannerHeight(), // 使用动态计算的高度
          width: '100%',
        }}
        onMove={(_e: object, a: number) => {
          // 防止在快速切换时重复触发
          const now = Date.now()
          const timeSinceLastSwitch =
            lastSwitchTimeRef.current === 0 ? 1000 : now - lastSwitchTimeRef.current

          console.log(
            `onMove 触发: ${a}, 当前时间: ${now}, 上次切换时间: ${lastSwitchTimeRef.current}, 距离上次切换: ${timeSinceLastSwitch}ms`,
          )

          if (timeSinceLastSwitch < 500) {
            // 增加防抖时间到500ms
            console.log(`onMove 被防抖阻止，间隔: ${timeSinceLastSwitch}ms`)
            return // 如果切换太频繁，忽略
          }

          console.log(`onMove 执行切换到: ${a}`)
          lastSwitchTimeRef.current = now
          stopDotsAnimation()
          setBannerIndex(a)
          handleSideChange(a)
        }}>
        {bannerDataFiltered?.map((item, index) => (
          <SplideSlide
            key={index}
            onMouseEnter={() => {
              stopDotsAnimation()
            }}
            onMouseLeave={() => {
              playDotsAnimation(index)
            }}>
            <Link
              className="relative block h-full w-full"
              href={item?.button_url?.url || '#'}
              onClick={(e) => {
                if (!item?.button_url?.url) {
                  e.preventDefault()
                  return
                }

                // 上报banner图片点击事件
                reportEvent(TRACK_EVENT.shop_homepage_banner_picture_click, {
                  banner_id: item?.id || '',
                  banner_name: item?.title || '',
                })

                // 上报top banner图片点击事件
                reportEvent(TRACK_EVENT.shop_homepage_top_banner_picture_click, {
                  banner_id: item?.id || '',
                  banner_name: item?.title || '',
                })
              }}>
              <div className="absolute inset-0 flex h-full w-full flex-col items-center overflow-hidden">
                {item?.pc_video_url ? (
                  renderVideo(item, index)
                ) : (
                  <div className="absolute inset-0 h-full w-full overflow-hidden">
                    <Image
                      src={item?.image_url_desktop || ''}
                      alt={item?.title || ''}
                      fill
                      className="object-cover"
                      priority={index === 0} // 第一张图片最高优先级
                      sizes="100vw"
                      quality={index === 0 ? 95 : 85} // 第一张图片高质量
                      style={{ objectPosition: 'center center' }}
                    />
                  </div>
                )}

                {/* 内容区域 */}
                <div className="relative mx-auto">
                  <div className="flex flex-col items-center pt-[66.5px] text-white">
                    {item?.subtitle && (
                      <div className="mb-[8px] truncate text-center font-miSansMedium380 text-[18px] leading-[120%] 2xl:text-[20px]">
                        {item?.subtitle}
                      </div>
                    )}
                    {item?.title && (
                      <div className="mb-[8px] truncate text-center font-miSansSemibold520 text-[40px] leading-[120%] 2xl:text-[64px]">
                        {item?.title}
                      </div>
                    )}
                    {item?.description && (
                      <div className="mb-[24px] truncate text-center font-miSansMedium380 text-[16px] leading-[140%] 2xl:text-[24px]">
                        {item?.description}
                      </div>
                    )}
                    {item?.button_text && (
                      <Button
                        className={clsx(
                          'min-w-[146px] font-miSansDemiBold450 text-[16px] leading-[20px]',
                          item?.button_arrow ? 'justify-between' : 'justify-center',
                        )}
                        type="primary"
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()

                          // 上报查看详情按钮点击事件
                          reportEvent(TRACK_EVENT.shop_homepage_top_banner_detail_button_click, {
                            banner_id: item?.id || '',
                            banner_name: item?.title || '',
                          })
                        }}>
                        {item?.button_text}
                        {item.button_arrow && <IconArrow color="#fff" size={20} rotate={-90} />}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          </SplideSlide>
        ))}
      </Splide>

      {/* 选项卡 */}
      {bannerTotal > 1 && (
        <>
          <div className="absolute bottom-0 left-0 h-[80px] w-full bg-[linear-gradient(360deg,rgba(0,0,0,0.5)_-71.82%,rgba(0,0,0,0)_88.64%)] xll:h-[106px]">
            <div className="max-container-no-mb">
              <div className="mx-auto flex w-fit justify-center">
                <div className="flex h-fit items-center justify-center text-[16px] text-white">
                  {bannerDataFiltered?.map((item, index) => (
                    <div
                      key={index}
                      className={cn(
                        'relative flex cursor-pointer items-center justify-center border-t border-gray-3 px-[20px] pt-[11px]',
                      )}
                      onClick={() => {
                        handleClickDot(index)
                      }}>
                      <div className={styles.progressContainer}>
                        <div className={`progressLine ${styles.progressLine}`} />
                      </div>

                      <span
                        className={cn(
                          'relative flex h-[54px] items-center whitespace-nowrap px-[5px] text-[16px] leading-[22px] text-white',
                          activeIndex === index
                            ? '!text-white before:font-miSansSemibold520 before:!text-white before:!text-opacity-100'
                            : '!text-opacity-60',
                          {
                            'invisible before:visible before:absolute before:inset-0 before:flex before:items-center before:justify-center before:content-[attr(data-value)]':
                              activeIndex === index,
                          },
                        )}
                        data-value={item?.bottom_title?.trim() || ''}>
                        {item?.bottom_title?.trim() || ''}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 top-0 ml-[48px] hidden text-white group-hover:block">
            <div className="flex h-full items-center justify-center">
              <button
                className="flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full bg-black/40 transition-colors duration-200 hover:bg-black/30 focus:bg-black/65 focus:outline-none active:bg-black/65"
                onClick={handlePrev}
                onMouseLeave={(e) => e.currentTarget.blur()}>
                <Arrow rotate={180} color="currentColor" />
              </button>
            </div>
          </div>
          <div className="absolute bottom-0 right-0 top-0 mr-[48px] hidden text-white group-hover:block">
            <div className="flex h-full items-center justify-center">
              <button
                className="flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full bg-black/40 transition-colors duration-200 hover:bg-black/30 focus:bg-black/65 focus:outline-none active:bg-black/65"
                onClick={handleNext}
                onMouseLeave={(e) => e.currentTarget.blur()}>
                <Arrow color="currentColor" />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default Banner
